body {
    font-family: "Segoe UI", sans-serif;
    background: #f9f9fc;
    padding: 40px;
    color: #333;
  }
  
  .container {
    max-width: 500px;
    margin: auto;
  }
  
  h1 {
    font-size: 1.5rem;
    margin-bottom: 30px;
  }
  
  .card {
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.05);
    margin-bottom: 20px;
  }
  
  .card.secondary {
    border-left: 4px solid #cfcfff;
  }
  
  .section-title {
    font-weight: bold;
    margin-bottom: 12px;
  }
  
  input {
    width: 100%;
    padding: 10px;
    font-size: 1rem;
    margin: 8px 0;
    border: 1px solid #ccc;
    border-radius: 8px;
  }
  
  button {
    background-color: #6c63ff;
    color: white;
    border: none;
    padding: 12px;
    margin-top: 10px;
    width: 100%;
    border-radius: 8px;
    font-size: 1rem;
    cursor: pointer;
    transition: background 0.3s;
  }
  
  button:hover {
    background-color: #594ff4;
  }
  
  small {
    display: block;
    margin-top: 4px;
    color: #888;
  }
  