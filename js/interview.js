// Interview Session Manager
class InterviewSession {
    constructor() {
        this.currentAnswerMode = 'text';
        this.isRecording = false;
        this.recordingStartTime = null;
        this.recordingTimer = null;
        this.currentAudio = null;
        this.answers = [];
        this.currentRecordingBlob = null;
        
        this.initializeSession();
        this.setupEventListeners();
    }

    async initializeSession() {
        try {
            // Show loading
            this.showLoading('Memuat sesi wawancara...');

            // Get interview parameters from URL or localStorage
            const urlParams = new URLSearchParams(window.location.search);
            const bidang = urlParams.get('bidang') || localStorage.getItem('interview_bidang') || 'Software Engineering';
            const posisi = urlParams.get('posisi') || localStorage.getItem('interview_posisi') || 'Frontend Developer';

            // Update UI with interview info
            document.getElementById('bidang-info').textContent = bidang;
            document.getElementById('posisi-info').textContent = posisi;

            // Generate questions
            questionGenerator.generateQuestions(bidang, posisi, 8);
            
            // Initialize speech recognition
            if (speechToText.recognition) {
                this.setupSpeechRecognition();
            } else {
                // Hide voice tab if not supported
                document.querySelector('[data-tab="voice"]').style.display = 'none';
            }

            // Initialize audio recorder
            await audioRecorder.initialize();

            // Load first question
            await this.loadCurrentQuestion();

            this.hideLoading();
        } catch (error) {
            console.error('Error initializing interview session:', error);
            this.hideLoading();
            alert('Terjadi kesalahan saat memuat sesi wawancara. Silakan coba lagi.');
        }
    }

    setupEventListeners() {
        // Audio control buttons
        document.getElementById('play-question-btn').addEventListener('click', () => {
            this.playCurrentQuestion();
        });

        document.getElementById('repeat-question-btn').addEventListener('click', () => {
            this.playCurrentQuestion();
        });

        // Answer mode switching
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const mode = e.target.dataset.tab;
                this.switchAnswerMode(mode);
            });
        });
    }

    setupSpeechRecognition() {
        speechToText.onStart = () => {
            console.log('Speech recognition started');
        };

        speechToText.onResult = (result) => {
            const transcriptDiv = document.getElementById('transcript-text');
            transcriptDiv.textContent = result.final + result.interim;
            
            if (result.isFinal && result.final.trim()) {
                document.getElementById('voice-transcript').style.display = 'block';
            }
        };

        speechToText.onError = (error) => {
            console.error('Speech recognition error:', error);
            this.stopRecording();
            alert('Terjadi kesalahan pada pengenalan suara. Silakan coba lagi.');
        };

        speechToText.onEnd = () => {
            this.stopRecording();
        };
    }

    async loadCurrentQuestion() {
        const questionData = questionGenerator.getNextQuestion();
        if (!questionData) {
            this.finishInterview();
            return;
        }

        // Update question display
        document.getElementById('question-number').textContent = 
            `Pertanyaan ${questionData.number} dari ${questionData.total}`;
        document.getElementById('question-text').textContent = questionData.question;

        // Update progress
        const progress = (questionData.number / questionData.total) * 100;
        document.getElementById('progress-fill').style.width = `${progress}%`;
        document.getElementById('progress-text').textContent = 
            `${questionData.number} dari ${questionData.total} pertanyaan`;

        // Update navigation buttons
        document.getElementById('prev-btn').style.display = 
            questionData.number === 1 ? 'none' : 'inline-block';
        
        if (questionData.isLast) {
            document.getElementById('next-btn').style.display = 'none';
            document.getElementById('finish-btn').style.display = 'inline-block';
        } else {
            document.getElementById('next-btn').style.display = 'inline-block';
            document.getElementById('finish-btn').style.display = 'none';
        }

        // Auto-play question if TTS is available
        setTimeout(() => {
            this.playCurrentQuestion();
        }, 500);
    }

    async playCurrentQuestion() {
        const questionText = document.getElementById('question-text').textContent;
        
        if (!questionText || questionText === 'Memuat pertanyaan...') {
            return;
        }

        try {
            // Show audio status
            document.getElementById('audio-status').style.display = 'block';
            
            // Disable audio buttons
            document.getElementById('play-question-btn').disabled = true;
            document.getElementById('repeat-question-btn').disabled = true;

            // Generate and play audio
            await elevenLabsTTS.speak(questionText);

        } catch (error) {
            console.error('Error playing question:', error);
            // Fallback: use browser's built-in TTS if available
            if ('speechSynthesis' in window) {
                const utterance = new SpeechSynthesisUtterance(questionText);
                utterance.lang = 'id-ID';
                speechSynthesis.speak(utterance);
            }
        } finally {
            // Hide audio status and re-enable buttons
            document.getElementById('audio-status').style.display = 'none';
            document.getElementById('play-question-btn').disabled = false;
            document.getElementById('repeat-question-btn').disabled = false;
        }
    }

    switchAnswerMode(mode) {
        this.currentAnswerMode = mode;

        // Update tab buttons
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.toggle('active', btn.dataset.tab === mode);
        });

        // Update answer modes
        document.querySelectorAll('.answer-mode').forEach(modeDiv => {
            modeDiv.classList.toggle('active', modeDiv.id === `${mode}-answer-mode`);
        });

        // Stop any ongoing recording when switching modes
        if (this.isRecording) {
            this.stopRecording();
        }
    }

    toggleRecording() {
        if (this.isRecording) {
            this.stopRecording();
        } else {
            this.startRecording();
        }
    }

    startRecording() {
        if (!speechToText.recognition) {
            alert('Fitur pengenalan suara tidak didukung di browser ini.');
            return;
        }

        // Clear previous transcript
        document.getElementById('transcript-text').textContent = '';
        document.getElementById('voice-transcript').style.display = 'none';

        // Start speech recognition
        if (speechToText.startListening()) {
            this.isRecording = true;
            this.recordingStartTime = Date.now();
            
            // Update UI
            const recordBtn = document.getElementById('record-btn');
            recordBtn.classList.add('recording');
            recordBtn.querySelector('.record-text').textContent = 'Berhenti Merekam';
            
            // Show recording status
            document.getElementById('recording-status').style.display = 'block';
            
            // Start timer
            this.startRecordingTimer();

            // Also start audio recording for backup
            audioRecorder.startRecording();
        }
    }

    stopRecording() {
        if (!this.isRecording) return;

        this.isRecording = false;
        speechToText.stopListening();

        // Update UI
        const recordBtn = document.getElementById('record-btn');
        recordBtn.classList.remove('recording');
        recordBtn.querySelector('.record-text').textContent = 'Mulai Merekam';
        
        // Hide recording status
        document.getElementById('recording-status').style.display = 'none';
        
        // Stop timer
        this.stopRecordingTimer();

        // Stop audio recording
        audioRecorder.stopRecording().then(result => {
            if (result) {
                this.currentRecordingBlob = result;
                document.getElementById('play-recording-btn').style.display = 'inline-block';
            }
        });
    }

    startRecordingTimer() {
        this.recordingTimer = setInterval(() => {
            const elapsed = Date.now() - this.recordingStartTime;
            const minutes = Math.floor(elapsed / 60000);
            const seconds = Math.floor((elapsed % 60000) / 1000);
            document.getElementById('recording-time').textContent = 
                `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }, 1000);
    }

    stopRecordingTimer() {
        if (this.recordingTimer) {
            clearInterval(this.recordingTimer);
            this.recordingTimer = null;
        }
    }

    clearTranscript() {
        document.getElementById('transcript-text').textContent = '';
        document.getElementById('voice-transcript').style.display = 'none';
        speechToText.clearTranscript();
        this.currentRecordingBlob = null;
        document.getElementById('play-recording-btn').style.display = 'none';
    }

    playRecording() {
        if (this.currentRecordingBlob && this.currentRecordingBlob.url) {
            const audio = new Audio(this.currentRecordingBlob.url);
            audio.play();
        }
    }

    saveCurrentAnswer() {
        const questionData = questionGenerator.getCurrentQuestionInfo();
        const questionText = document.getElementById('question-text').textContent;
        
        let answer = '';
        if (this.currentAnswerMode === 'text') {
            answer = document.getElementById('answer-text').value.trim();
        } else {
            answer = document.getElementById('transcript-text').textContent.trim();
        }

        this.answers[questionData.current - 1] = {
            question: questionText,
            answer: answer,
            mode: this.currentAnswerMode,
            timestamp: new Date().toISOString()
        };
    }

    nextQuestion() {
        this.saveCurrentAnswer();
        
        if (questionGenerator.moveToNext()) {
            this.loadCurrentQuestion();
            this.clearAnswerInputs();
        } else {
            this.finishInterview();
        }
    }

    previousQuestion() {
        this.saveCurrentAnswer();
        
        if (questionGenerator.moveToPrevious()) {
            this.loadCurrentQuestion();
            this.loadPreviousAnswer();
        }
    }

    clearAnswerInputs() {
        document.getElementById('answer-text').value = '';
        this.clearTranscript();
    }

    loadPreviousAnswer() {
        const questionData = questionGenerator.getCurrentQuestionInfo();
        const savedAnswer = this.answers[questionData.current - 1];
        
        if (savedAnswer) {
            if (savedAnswer.mode === 'text') {
                document.getElementById('answer-text').value = savedAnswer.answer;
                this.switchAnswerMode('text');
            } else {
                document.getElementById('transcript-text').textContent = savedAnswer.answer;
                if (savedAnswer.answer) {
                    document.getElementById('voice-transcript').style.display = 'block';
                }
                this.switchAnswerMode('voice');
            }
        }
    }

    finishInterview() {
        this.saveCurrentAnswer();
        
        // Save interview results
        const interviewResults = {
            bidang: document.getElementById('bidang-info').textContent,
            posisi: document.getElementById('posisi-info').textContent,
            answers: this.answers,
            completedAt: new Date().toISOString()
        };
        
        localStorage.setItem('interview_results', JSON.stringify(interviewResults));
        
        // Redirect to results page
        alert('Wawancara selesai! Terima kasih atas partisipasi Anda.');
        window.location.href = 'dashboard.html';
    }

    showLoading(message = 'Memuat...') {
        document.getElementById('loading-text').textContent = message;
        document.getElementById('loading-overlay').style.display = 'flex';
    }

    hideLoading() {
        document.getElementById('loading-overlay').style.display = 'none';
    }
}

// Global functions for HTML onclick handlers
function switchAnswerMode(mode) {
    interviewSession.switchAnswerMode(mode);
}

function toggleRecording() {
    interviewSession.toggleRecording();
}

function clearTranscript() {
    interviewSession.clearTranscript();
}

function playRecording() {
    interviewSession.playRecording();
}

function nextQuestion() {
    interviewSession.nextQuestion();
}

function previousQuestion() {
    interviewSession.previousQuestion();
}

function finishInterview() {
    interviewSession.finishInterview();
}

// Initialize interview session when page loads
let interviewSession;
document.addEventListener('DOMContentLoaded', () => {
    interviewSession = new InterviewSession();
});
