// AI Question Generator untuk Wawancara
class InterviewQuestionGenerator {
    constructor() {
        this.questionBank = {
            // Pertanyaan umum untuk semua bidang
            general: [
                "Ceritakan tentang diri Anda dan mengapa Anda tertarik dengan posisi ini.",
                "Apa kelebihan dan kelemahan Anda?",
                "Mengapa Anda ingin bekerja di perusahaan ini?",
                "Di mana Anda melihat diri Anda dalam 5 tahun ke depan?",
                "Ceritakan tentang pencapaian terbesar Anda.",
                "Bagaimana cara Anda menangani tekanan dan deadline?",
                "Mengapa Anda meninggalkan pekerjaan sebelumnya?",
                "Apa yang Anda ketahui tentang perusahaan kami?",
                "Berapa ekspektasi gaji Anda?",
                "Apakah Anda memiliki pertanyaan untuk kami?"
            ],

            // Pertanyaan spesifik berdasarkan bidang
            "Software Engineering": [
                "Jelaskan perbedaan antara programming language yang Anda kuasai.",
                "Bagaimana Anda menangani debugging pada aplikasi yang kompleks?",
                "Ceritakan tentang proyek coding terbesar yang pernah Anda kerjakan.",
                "Apa pendapat Anda tentang clean code dan best practices?",
                "Bagaimana Anda mengikuti perkembangan teknologi terbaru?",
                "Jelaskan konsep Object-Oriented Programming.",
                "Apa pengalaman Anda dengan version control seperti Git?",
                "Bagaimana Anda menangani code review dan feedback?"
            ],

            "Data Science": [
                "Jelaskan workflow typical dalam proyek data science.",
                "Apa perbedaan antara supervised dan unsupervised learning?",
                "Bagaimana Anda menangani missing data dalam dataset?",
                "Ceritakan tentang proyek machine learning yang pernah Anda kerjakan.",
                "Apa tools dan library yang sering Anda gunakan untuk analisis data?",
                "Bagaimana Anda memvalidasi model machine learning?",
                "Jelaskan konsep overfitting dan cara mengatasinya.",
                "Bagaimana Anda mengkomunikasikan hasil analisis kepada non-technical stakeholders?"
            ],

            "Marketing": [
                "Bagaimana Anda mengukur keberhasilan campaign marketing?",
                "Apa strategi digital marketing yang paling efektif menurut Anda?",
                "Ceritakan tentang campaign marketing yang pernah Anda kelola.",
                "Bagaimana Anda menentukan target audience untuk produk baru?",
                "Apa pendapat Anda tentang social media marketing saat ini?",
                "Bagaimana Anda menangani budget marketing yang terbatas?",
                "Jelaskan perbedaan antara B2B dan B2C marketing.",
                "Bagaimana Anda menganalisis kompetitor?"
            ],

            "Human Resources": [
                "Bagaimana Anda menangani konflik antar karyawan?",
                "Apa strategi Anda untuk meningkatkan employee engagement?",
                "Ceritakan pengalaman Anda dalam proses recruitment.",
                "Bagaimana Anda menangani performance review yang sulit?",
                "Apa pendapat Anda tentang remote work dan hybrid working?",
                "Bagaimana Anda memastikan diversity dan inclusion di workplace?",
                "Jelaskan proses onboarding yang ideal menurut Anda.",
                "Bagaimana Anda menangani employee retention?"
            ],

            "Finance": [
                "Jelaskan perbedaan antara cash flow dan profit.",
                "Bagaimana Anda melakukan financial forecasting?",
                "Apa pengalaman Anda dengan financial modeling?",
                "Bagaimana Anda mengevaluasi investasi atau project?",
                "Jelaskan konsep risk management dalam finance.",
                "Apa tools yang Anda gunakan untuk financial analysis?",
                "Bagaimana Anda menangani budget planning dan monitoring?",
                "Ceritakan tentang tantangan terbesar dalam financial reporting."
            ]
        };

        this.currentQuestions = [];
        this.currentIndex = 0;
    }

    // Generate pertanyaan berdasarkan bidang dan posisi
    generateQuestions(bidang, posisi, jumlah = 8) {
        const questions = [];
        
        // Ambil pertanyaan umum (40%)
        const generalQuestions = this.getRandomQuestions(this.questionBank.general, Math.ceil(jumlah * 0.4));
        questions.push(...generalQuestions);

        // Ambil pertanyaan spesifik bidang (60%)
        const specificQuestions = this.getRandomQuestions(
            this.questionBank[bidang] || this.questionBank.general, 
            jumlah - generalQuestions.length
        );
        questions.push(...specificQuestions);

        // Shuffle pertanyaan
        this.currentQuestions = this.shuffleArray(questions);
        this.currentIndex = 0;

        return this.currentQuestions;
    }

    // Ambil pertanyaan random dari array
    getRandomQuestions(questionArray, count) {
        const shuffled = this.shuffleArray([...questionArray]);
        return shuffled.slice(0, Math.min(count, shuffled.length));
    }

    // Shuffle array
    shuffleArray(array) {
        const shuffled = [...array];
        for (let i = shuffled.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
        }
        return shuffled;
    }

    // Get pertanyaan berikutnya
    getNextQuestion() {
        if (this.currentIndex < this.currentQuestions.length) {
            return {
                question: this.currentQuestions[this.currentIndex],
                number: this.currentIndex + 1,
                total: this.currentQuestions.length,
                isLast: this.currentIndex === this.currentQuestions.length - 1
            };
        }
        return null;
    }

    // Get pertanyaan sebelumnya
    getPreviousQuestion() {
        if (this.currentIndex > 0) {
            this.currentIndex--;
            return this.getNextQuestion();
        }
        return null;
    }

    // Move ke pertanyaan berikutnya
    moveToNext() {
        if (this.currentIndex < this.currentQuestions.length - 1) {
            this.currentIndex++;
            return true;
        }
        return false;
    }

    // Move ke pertanyaan sebelumnya
    moveToPrevious() {
        if (this.currentIndex > 0) {
            this.currentIndex--;
            return true;
        }
        return false;
    }

    // Get current question info
    getCurrentQuestionInfo() {
        return {
            current: this.currentIndex + 1,
            total: this.currentQuestions.length,
            progress: ((this.currentIndex + 1) / this.currentQuestions.length) * 100
        };
    }

    // Reset ke pertanyaan pertama
    reset() {
        this.currentIndex = 0;
    }

    // Get semua pertanyaan yang sudah di-generate
    getAllQuestions() {
        return this.currentQuestions;
    }

    // Add custom question
    addCustomQuestion(question, bidang = 'general') {
        if (!this.questionBank[bidang]) {
            this.questionBank[bidang] = [];
        }
        this.questionBank[bidang].push(question);
    }

    // Get available bidang
    getAvailableBidang() {
        return Object.keys(this.questionBank).filter(key => key !== 'general');
    }
}

// Initialize question generator
const questionGenerator = new InterviewQuestionGenerator();

// Export for use in other files
window.questionGenerator = questionGenerator;
