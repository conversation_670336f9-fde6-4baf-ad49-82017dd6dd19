// ElevenLabs TTS Integration
class ElevenLabsTTS {
    constructor() {
        this.apiKey = '***************************************************';
        this.baseUrl = 'https://api.elevenlabs.io/v1';
        this.defaultVoiceId = 'pNInz6obpgDQGcFmaJgB'; // Adam voice (English)
        this.audioCache = new Map(); // Cache untuk audio yang sudah di-generate
    }

    // Mendapatkan daftar voice yang tersedia
    async getVoices() {
        try {
            const response = await fetch(`${this.baseUrl}/voices`, {
                method: 'GET',
                headers: {
                    'xi-api-key': this.apiKey,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            return data.voices;
        } catch (error) {
            console.error('Error fetching voices:', error);
            throw error;
        }
    }

    // Convert text to speech
    async textToSpeech(text, voiceId = null, options = {}) {
        const selectedVoiceId = voiceId || this.defaultVoiceId;
        const cacheKey = `${selectedVoiceId}-${text}`;

        // Check cache first
        if (this.audioCache.has(cacheKey)) {
            return this.audioCache.get(cacheKey);
        }

        try {
            const requestBody = {
                text: text,
                model_id: options.model_id || "eleven_monolingual_v1",
                voice_settings: {
                    stability: options.stability || 0.5,
                    similarity_boost: options.similarity_boost || 0.5,
                    style: options.style || 0.0,
                    use_speaker_boost: options.use_speaker_boost || true
                }
            };

            const response = await fetch(`${this.baseUrl}/text-to-speech/${selectedVoiceId}`, {
                method: 'POST',
                headers: {
                    'xi-api-key': this.apiKey,
                    'Content-Type': 'application/json',
                    'Accept': 'audio/mpeg'
                },
                body: JSON.stringify(requestBody)
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const audioBlob = await response.blob();
            const audioUrl = URL.createObjectURL(audioBlob);

            // Cache the result
            this.audioCache.set(cacheKey, audioUrl);

            return audioUrl;
        } catch (error) {
            console.error('Error generating speech:', error);
            throw error;
        }
    }

    // Play audio dari URL
    async playAudio(audioUrl, onEnd = null) {
        return new Promise((resolve, reject) => {
            const audio = new Audio(audioUrl);
            
            audio.onloadeddata = () => {
                console.log('Audio loaded successfully');
            };

            audio.onended = () => {
                console.log('Audio playback finished');
                if (onEnd) onEnd();
                resolve();
            };

            audio.onerror = (error) => {
                console.error('Audio playback error:', error);
                reject(error);
            };

            audio.play().catch(reject);
        });
    }

    // Speak text directly (shorthand method)
    async speak(text, voiceId = null, options = {}) {
        try {
            const audioUrl = await this.textToSpeech(text, voiceId, options);
            await this.playAudio(audioUrl);
            return audioUrl;
        } catch (error) {
            console.error('Error in speak method:', error);
            throw error;
        }
    }

    // Clear audio cache
    clearCache() {
        // Revoke all object URLs to free memory
        for (const url of this.audioCache.values()) {
            URL.revokeObjectURL(url);
        }
        this.audioCache.clear();
    }

    // Get user's quota information
    async getUserInfo() {
        try {
            const response = await fetch(`${this.baseUrl}/user`, {
                method: 'GET',
                headers: {
                    'xi-api-key': this.apiKey
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('Error fetching user info:', error);
            throw error;
        }
    }
}

// Initialize ElevenLabs TTS instance
const elevenLabsTTS = new ElevenLabsTTS();

// Export for use in other files
window.elevenLabsTTS = elevenLabsTTS;
