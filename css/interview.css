/* Interview Session Styles */

.interview-header {
  text-align: center;
  margin-bottom: 2rem;
}

.interview-info {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin-top: 1rem;
  font-size: 0.9rem;
  color: #666;
}

.interview-card {
  max-width: 800px;
  margin: 0 auto;
}

/* Question Section */
.question-section {
  border-bottom: 2px solid #f0f0f0;
  padding-bottom: 1.5rem;
  margin-bottom: 1.5rem;
}

.question-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.question-number {
  font-size: 0.9rem;
  color: #6c63ff;
  font-weight: 600;
}

.audio-controls {
  display: flex;
  gap: 0.5rem;
}

.btn-audio {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 1.2rem;
}

.btn-audio:hover {
  background: #6c63ff;
  color: white;
  transform: scale(1.05);
}

.btn-audio:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.question-text {
  font-size: 1.2rem;
  line-height: 1.6;
  color: #333;
  font-weight: 500;
}

.audio-status {
  margin-top: 1rem;
  padding: 0.75rem 1rem;
  background: #e8f4fd;
  border-left: 4px solid #2196f3;
  border-radius: 4px;
}

.status-text {
  color: #1976d2;
  font-weight: 500;
}

/* Answer Section */
.answer-tabs {
  display: flex;
  margin-bottom: 1rem;
  border-bottom: 1px solid #e9ecef;
}

.tab-btn {
  background: none;
  border: none;
  padding: 0.75rem 1.5rem;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.2s ease;
  font-size: 0.9rem;
  color: #666;
}

.tab-btn.active {
  color: #6c63ff;
  border-bottom-color: #6c63ff;
  font-weight: 600;
}

.tab-btn:hover {
  color: #6c63ff;
  background: #f8f9fa;
}

.answer-mode {
  display: none;
}

.answer-mode.active {
  display: block;
}

/* Text Answer Mode */
#answer-text {
  width: 100%;
  min-height: 120px;
  padding: 1rem;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 1rem;
  line-height: 1.5;
  resize: vertical;
  transition: border-color 0.2s ease;
}

#answer-text:focus {
  outline: none;
  border-color: #6c63ff;
  box-shadow: 0 0 0 3px rgba(108, 99, 255, 0.1);
}

/* Voice Answer Mode */
.voice-controls {
  text-align: center;
  margin-bottom: 1.5rem;
}

.btn-record {
  background: #ff4757;
  color: white;
  border: none;
  border-radius: 50px;
  padding: 1rem 2rem;
  font-size: 1rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0 auto;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(255, 71, 87, 0.3);
}

.btn-record:hover {
  background: #ff3742;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 71, 87, 0.4);
}

.btn-record.recording {
  background: #ff6b7a;
  animation: pulse 1.5s infinite;
}

.record-icon {
  font-size: 1.2rem;
}

.recording-status {
  margin-top: 1rem;
  text-align: center;
}

.recording-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  color: #ff4757;
  font-weight: 500;
}

.pulse-dot {
  width: 12px;
  height: 12px;
  background: #ff4757;
  border-radius: 50%;
  animation: pulse 1s infinite;
}

.recording-time {
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
}

.voice-transcript {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1rem;
  margin-top: 1rem;
}

.voice-transcript h4 {
  margin: 0 0 0.5rem 0;
  color: #333;
  font-size: 1rem;
}

.transcript-text {
  background: white;
  padding: 1rem;
  border-radius: 6px;
  border: 1px solid #e9ecef;
  min-height: 80px;
  line-height: 1.5;
  color: #333;
}

.transcript-controls {
  display: flex;
  gap: 0.5rem;
  margin-top: 0.75rem;
}

.btn-small {
  background: #6c63ff;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.5rem 1rem;
  font-size: 0.8rem;
  cursor: pointer;
  transition: background 0.2s ease;
}

.btn-small:hover {
  background: #5a52d5;
}

/* Navigation Controls */
.controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 2rem;
  gap: 1rem;
}

.btn-secondary, .btn-primary, .btn-success {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: 500;
}

.btn-secondary {
  background: #f8f9fa;
  color: #666;
  border: 1px solid #e9ecef;
}

.btn-secondary:hover {
  background: #e9ecef;
}

.btn-primary {
  background: #6c63ff;
  color: white;
}

.btn-primary:hover {
  background: #5a52d5;
  transform: translateY(-1px);
}

.btn-success {
  background: #2ed573;
  color: white;
}

.btn-success:hover {
  background: #26d467;
  transform: translateY(-1px);
}

/* Progress Bar */
.progress-container {
  margin-top: 2rem;
  text-align: center;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #f0f0f0;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #6c63ff, #a29bfe);
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 0.9rem;
  color: #666;
}

/* Loading Overlay */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-content {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  text-align: center;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #6c63ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

/* Animations */
@keyframes pulse {
  0% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.7; transform: scale(1.05); }
  100% { opacity: 1; transform: scale(1); }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .interview-info {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .question-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .audio-controls {
    align-self: flex-end;
  }
  
  .controls {
    flex-direction: column;
  }
  
  .btn-secondary, .btn-primary, .btn-success {
    width: 100%;
  }
}
