<!DOCTYPE html>
<html lang="id">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><PERSON><PERSON></title>
  <link rel="stylesheet" href="style.css" />
  <style>
    .overlay {
      position: fixed;
      top: 0; left: 0; right: 0; bottom: 0;
      background: rgba(0, 0, 0, 0.6);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 999;
    }
    .modal {
      background: #fff;
      padding: 30px;
      border-radius: 12px;
      text-align: center;
      max-width: 400px;
      box-shadow: 0 10px 25px rgba(0,0,0,0.2);
    }
    .modal h2 {
      margin: 16px 0 8px;
    }
    .modal p {
      color: #444;
      margin-bottom: 24px;
    }
    .modal button {
      padding: 12px 24px;
      margin: 0 10px;
      border: none;
      border-radius: 8px;
      font-size: 1rem;
      cursor: pointer;
    }
    .btn-cancel {
      background: white;
      color: #444;
      border: 1px solid #ccc;
    }
    .btn-start {
      background: #6c63ff;
      color: white;
    }
    .countdown {
      font-size: 5rem;
      color: white;
      text-align: center;
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      z-index: 999;
    }
  </style>
</head>
<body style="background: #1d1b2f; color: white;">
  <div id="modal" class="overlay">
    <div class="modal">
      <div style="font-size: 3rem; color: #6c63ff;">▶️</div>
      <h2>Mulai Wawancara?</h2>
      <p>Sebelum memulai wawancara pastikan anda berada di lingkungan yang kondusif, tekan <strong>"Mulai"</strong> untuk memulai sesi wawancara.</p>
      <button class="btn-cancel" onclick="window.location.href='index.html'">Batalkan Sesi</button>
      <button class="btn-start" onclick="startInterview()">Mulai Sesi</button>
    </div>
  </div>

  <div id="countdown" class="countdown" style="display:none;"></div>

  <script>
    function startInterview() {
      document.getElementById("modal").style.display = "none";
      const countdownDiv = document.getElementById("countdown");
      countdownDiv.style.display = "block";
      let counter = 3;
      countdownDiv.textContent = counter;
      const interval = setInterval(() => {
        counter--;
        if (counter === 0) {
          clearInterval(interval);
          countdownDiv.style.display = "none";
          // Redirect or start the interview interface
          //alert("Sesi dimulai!"); // ganti dengan halaman wawancara berikutnya
          //window.location.href = "session.html"; // atau halaman sesi yang kamu inginkan
          window.location.href = "dashboard.html";


        } else {
          countdownDiv.textContent = counter;
        }
      }, 1000);
    }
  </script>
</body>
</html>
