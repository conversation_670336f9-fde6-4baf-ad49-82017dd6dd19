<!DOCTYPE html>
<html lang="id">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><PERSON><PERSON> AI</title>
  <link rel="stylesheet" href="style.css" />
  <style>
    body {
      background-color: #1d1b2f;
      color: white;
      font-family: 'Segoe UI', sans-serif;
      margin: 0;
      padding: 0;
    }
    .session-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: space-between;
      height: 100vh;
      padding: 24px;
    }
    .interview-panel {
      display: flex;
      justify-content: center;
      gap: 24px;
      margin-top: 40px;
      width: 100%;
    }
    .panel-box {
      background-color: #2a2743;
      flex: 1;
      padding: 20px;
      border-radius: 16px;
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;
    }
    .panel-box img {
      width: 64px;
      height: 64px;
      margin-bottom: 16px;
    }
    .transcript {
      background-color: #2f2c4c;
      border-radius: 12px;
      padding: 16px;
      width: 100%;
      max-width: 800px;
      margin-top: 30px;
      height: 150px;
      overflow-y: auto;
      font-size: 14px;
      color: #ccc;
    }
    .controls {
      margin-top: 20px;
      text-align: center;
    }
    .controls button {
      margin: 0 8px;
      padding: 10px 20px;
      border: none;
      border-radius: 8px;
      background-color: #6c63ff;
      color: white;
      cursor: pointer;
      font-size: 1rem;
    }
    .footer {
      margin-top: auto;
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .footer button {
      background: #e74c3c;
      border: none;
      padding: 10px 20px;
      border-radius: 8px;
      color: white;
      font-weight: bold;
      cursor: pointer;
    }
    .timer {
      font-size: 1rem;
      color: #aaa;
    }
  </style>
</head>
<body>
  <div class="session-container">
    <div class="interview-panel">
      <div class="panel-box">
        <img src="https://cdn-icons-png.flaticon.com/512/4712/4712109.png" alt="AI Bot">
        <div id="ai-question">Pertanyaan akan muncul di sini...</div>
        <div class="user-name">AI Bot</div>
      </div>
      <div class="panel-box">
        <img src="https://cdn-icons-png.flaticon.com/512/149/149071.png" alt="User">
        <div class="controls">
          <button onclick="startRecording()">🎤 Mulai Jawab</button>
          <button onclick="nextQuestion()">➡️ Pertanyaan Berikutnya</button>
        </div>
        <div class="user-name">susanto431</div>
      </div>
    </div>

    <div class="transcript" id="transcript-log"></div>

    <div class="footer">
      <div class="timer">⏱ 15:00</div>
      <button onclick="endSession()">Akhiri Sesi</button>
    </div>
  </div>

  <script>
    const questions = [
      "Ceritakan tentang pengalaman kerja kamu yang paling menantang.",
      "Mengapa kamu tertarik dengan posisi ini?",
      "Bagaimana cara kamu menghadapi konflik dalam tim?",
      "Apa pencapaian terbesar kamu sejauh ini?",
      "Apa kelemahan terbesar yang kamu miliki dan bagaimana kamu menghadapinya?"
    ];

    let currentQuestion = 0;
    const questionBox = document.getElementById("ai-question");
    const transcriptLog = document.getElementById("transcript-log");

    function showQuestion() {
      questionBox.textContent = questions[currentQuestion] || "Sesi telah selesai.";
    }

    function nextQuestion() {
      currentQuestion++;
      showQuestion();
    }

    function endSession() {
      const confirmEnd = confirm("Yakin ingin mengakhiri sesi ini?");
      if (confirmEnd) {
        window.location.href = "index.html";
      }
    }

    // Speech Recognition
    let recognition;
    function startRecording() {
      if (!('webkitSpeechRecognition' in window)) {
        alert("Browser kamu tidak mendukung Speech Recognition. Coba pakai Chrome.");
        return;
      }

      recognition = new webkitSpeechRecognition();
      recognition.lang = 'id-ID';
      recognition.continuous = false;
      recognition.interimResults = false;

      recognition.onstart = () => {
        transcriptLog.innerHTML += `<div><em>📡 Merekam jawaban...</em></div>`;
      };

      recognition.onresult = (event) => {
        const hasil = event.results[0][0].transcript;
        transcriptLog.innerHTML += `<div><strong>🗣 Kamu:</strong> ${hasil}</div>`;
      };

      recognition.onerror = (event) => {
        transcriptLog.innerHTML += `<div style='color:tomato;'>❌ Error: ${event.error}</div>`;
      };

      recognition.onend = () => {
        transcriptLog.innerHTML += `<hr>`;
      };

      recognition.start();
    }

    showQuestion();
  </script>
</body>
</html>
