const bidangInput = document.getElementById("bidang");
const suggestionBox = document.getElementById("suggestions");
const posisiInput = document.getElementById("posisi");
const posisiBox = document.getElementById("positions");
const posisiContainer = document.getElementById("posisi-container");

let posisiPerBidang = {};

// Ambil data bidang dan posisi dari JSON eksternal
fetch("data/bidang_posisi.json")
  .then(res => res.json())
  .then(data => {
    posisiPerBidang = data;
    isiBidang();
  })
  .catch(error => {
    console.error("Gagal memuat bidang & posisi:", error);
  });

function isiBidang() {
  const jobTitles = Object.keys(posisiPerBidang);
  suggestionBox.innerHTML = "";

  jobTitles.sort().forEach(title => {
    const option = document.createElement("option");
    option.value = title;
    suggestionBox.appendChild(option);
  });

  bidangInput.placeholder = "Pilih atau ketik bidang...";
  bidangInput.disabled = false;
}

// Event: saat bidang dipilih, tampilkan posisi yang sesuai
bidangInput.addEventListener("input", () => {
  const bidangDipilih = bidangInput.value;
  posisiBox.innerHTML = "";
  posisiInput.value = "";

  if (posisiPerBidang[bidangDipilih]) {
    posisiPerBidang[bidangDipilih].forEach(posisi => {
      const option = document.createElement("option");
      option.value = posisi;
      posisiBox.appendChild(option);
    });
    posisiInput.disabled = false;
    posisiInput.placeholder = "Pilih atau ketik posisi...";
    posisiContainer.style.display = "block";
  } else {
    posisiInput.placeholder = "Posisi tidak tersedia";
    posisiInput.disabled = true;
    posisiContainer.style.display = "none";
  }
});

function mulaiLatihan() {
  const bidang = bidangInput.value;
  const posisi = posisiInput.value;

  if (!bidang || !posisi) {
    alert("Silakan pilih bidang dan posisi terlebih dahulu.");
    return;
  }

  // Save interview parameters
  localStorage.setItem('interview_bidang', bidang);
  localStorage.setItem('interview_posisi', posisi);

  // Redirect to interview session
  window.location.href = `interview-session.html?bidang=${encodeURIComponent(bidang)}&posisi=${encodeURIComponent(posisi)}`;
}
